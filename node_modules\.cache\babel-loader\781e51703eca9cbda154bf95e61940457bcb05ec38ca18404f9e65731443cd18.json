{"ast": null, "code": "'use strict';\n\nvar NATIVE_BIND = require('../internals/function-bind-native');\nvar FunctionPrototype = Function.prototype;\nvar call = FunctionPrototype.call;\n// eslint-disable-next-line es/no-function-prototype-bind -- safe\nvar uncurryThisWithBind = NATIVE_BIND && FunctionPrototype.bind.bind(call, call);\nmodule.exports = NATIVE_BIND ? uncurryThisWithBind : function (fn) {\n  return function () {\n    return call.apply(fn, arguments);\n  };\n};", "map": {"version": 3, "names": ["NATIVE_BIND", "require", "FunctionPrototype", "Function", "prototype", "call", "uncurryThisWithBind", "bind", "module", "exports", "fn", "apply", "arguments"], "sources": ["D:/Hotel/node_modules/core-js-pure/internals/function-uncurry-this.js"], "sourcesContent": ["'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar call = FunctionPrototype.call;\n// eslint-disable-next-line es/no-function-prototype-bind -- safe\nvar uncurryThisWithBind = NATIVE_BIND && FunctionPrototype.bind.bind(call, call);\n\nmodule.exports = NATIVE_BIND ? uncurryThisWithBind : function (fn) {\n  return function () {\n    return call.apply(fn, arguments);\n  };\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,mCAAmC,CAAC;AAE9D,IAAIC,iBAAiB,GAAGC,QAAQ,CAACC,SAAS;AAC1C,IAAIC,IAAI,GAAGH,iBAAiB,CAACG,IAAI;AACjC;AACA,IAAIC,mBAAmB,GAAGN,WAAW,IAAIE,iBAAiB,CAACK,IAAI,CAACA,IAAI,CAACF,IAAI,EAAEA,IAAI,CAAC;AAEhFG,MAAM,CAACC,OAAO,GAAGT,WAAW,GAAGM,mBAAmB,GAAG,UAAUI,EAAE,EAAE;EACjE,OAAO,YAAY;IACjB,OAAOL,IAAI,CAACM,KAAK,CAACD,EAAE,EAAEE,SAAS,CAAC;EAClC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}