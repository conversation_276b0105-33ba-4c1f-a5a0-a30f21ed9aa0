// Mock API for testing purposes
// Replace this with actual API calls in production

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Mock data
let mockCategories = [
  { id: 1, name: 'Appetizers', description: 'Start your meal with our delicious appetizers' },
  { id: 2, name: 'Main Courses', description: 'Hearty and satisfying main dishes' },
  { id: 3, name: 'Desser<PERSON>', description: 'Sweet treats to end your meal' },
  { id: 4, name: 'Beverages', description: 'Refreshing drinks and beverages' },
];

let mockItems = [
  {
    id: 1,
    name: 'Caesar Salad',
    description: 'Fresh romaine lettuce with parmesan cheese and croutons',
    price: 12.99,
    categoryId: 1,
    imageUrl: 'https://via.placeholder.com/300x200?text=Caesar+Salad'
  },
  {
    id: 2,
    name: 'Grilled Salmon',
    description: 'Fresh Atlantic salmon grilled to perfection',
    price: 24.99,
    categoryId: 2,
    imageUrl: 'https://via.placeholder.com/300x200?text=Grilled+Salmon'
  },
  {
    id: 3,
    name: 'Chocolate Cake',
    description: 'Rich chocolate cake with vanilla ice cream',
    price: 8.99,
    categoryId: 3,
    imageUrl: 'https://via.placeholder.com/300x200?text=Chocolate+Cake'
  },
  {
    id: 4,
    name: 'Fresh Orange Juice',
    description: 'Freshly squeezed orange juice',
    price: 4.99,
    categoryId: 4,
    imageUrl: 'https://via.placeholder.com/300x200?text=Orange+Juice'
  },
];

let nextId = 5;

// Mock API functions
export const mockAuthAPI = {
  login: async (credentials) => {
    await delay(1000);
    if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
      return {
        data: {
          token: 'mock-jwt-token-12345',
          user: { id: 1, email: '<EMAIL>', name: 'Admin' }
        }
      };
    } else {
      throw new Error('Invalid credentials');
    }
  },
  logout: async () => {
    await delay(500);
    return { data: { message: 'Logged out successfully' } };
  },
  isAuthenticated: () => !!localStorage.getItem('token'),
};

export const mockCategoriesAPI = {
  getAll: async () => {
    await delay(800);
    return { data: mockCategories };
  },
  create: async (category) => {
    await delay(1000);
    const newCategory = { ...category, id: nextId++ };
    mockCategories.push(newCategory);
    return { data: newCategory };
  },
  update: async (id, category) => {
    await delay(1000);
    const index = mockCategories.findIndex(cat => cat.id === id);
    if (index !== -1) {
      mockCategories[index] = { ...mockCategories[index], ...category };
      return { data: mockCategories[index] };
    }
    throw new Error('Category not found');
  },
  delete: async (id) => {
    await delay(800);
    mockCategories = mockCategories.filter(cat => cat.id !== id);
    return { data: { message: 'Category deleted' } };
  },
};

export const mockItemsAPI = {
  getAll: async () => {
    await delay(800);
    return { data: mockItems };
  },
  getByTableId: async (tableId) => {
    await delay(800);
    // In a real app, this would filter by table
    // For demo purposes, return all items
    return { data: mockItems };
  },
  create: async (item) => {
    await delay(1000);
    const newItem = { ...item, id: nextId++ };
    mockItems.push(newItem);
    return { data: newItem };
  },
  update: async (id, item) => {
    await delay(1000);
    const index = mockItems.findIndex(itm => itm.id === id);
    if (index !== -1) {
      mockItems[index] = { ...mockItems[index], ...item };
      return { data: mockItems[index] };
    }
    throw new Error('Item not found');
  },
  delete: async (id) => {
    await delay(800);
    mockItems = mockItems.filter(itm => itm.id !== id);
    return { data: { message: 'Item deleted' } };
  },
  uploadImage: async (id, formData) => {
    await delay(1500);
    // Mock image upload - in real app, this would upload to server
    const mockImageUrl = `https://via.placeholder.com/300x200?text=Item+${id}`;
    const index = mockItems.findIndex(itm => itm.id === id);
    if (index !== -1) {
      mockItems[index].imageUrl = mockImageUrl;
      return { data: { imageUrl: mockImageUrl } };
    }
    throw new Error('Item not found');
  },
};

export const mockQrCodeAPI = {
  getQRCode: async (tableId) => {
    await delay(1000);
    const menuUrl = `${window.location.origin}/menu/${tableId}`;
    return {
      data: {
        tableId,
        menuUrl,
        qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(menuUrl)}`
      }
    };
  },
};

// Export mock APIs for development
export const useMockAPI = process.env.NODE_ENV === 'development' && process.env.REACT_APP_USE_MOCK_API === 'true';
