import React, { useState, useEffect } from 'react';
import Navbar from '../components/Navbar';
import Modal from '../components/Modal';
import CategoryForm from '../components/CategoryForm';
import ItemForm from '../components/ItemForm';
import { categoriesAPI, itemsAPI, qrCodeAPI } from '../services/api';

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('categories');
  const [categories, setCategories] = useState([]);
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Modal states
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showItemModal, setShowItemModal] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [editingItem, setEditingItem] = useState(null);
  const [qrCodeData, setQrCodeData] = useState(null);

  useEffect(() => {
    loadCategories();
    loadItems();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const response = await categoriesAPI.getAll();
      setCategories(response.data);
    } catch (err) {
      setError('Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const loadItems = async () => {
    try {
      setLoading(true);
      const response = await itemsAPI.getAll();
      setItems(response.data);
    } catch (err) {
      setError('Failed to load items');
    } finally {
      setLoading(false);
    }
  };

  const handleCategorySubmit = async (categoryData) => {
    try {
      if (editingCategory) {
        await categoriesAPI.update(editingCategory.id, categoryData);
      } else {
        await categoriesAPI.create(categoryData);
      }
      await loadCategories();
      setShowCategoryModal(false);
      setEditingCategory(null);
    } catch (err) {
      setError('Failed to save category');
    }
  };

  const handleCategoryDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      try {
        await categoriesAPI.delete(id);
        await loadCategories();
      } catch (err) {
        setError('Failed to delete category');
      }
    }
  };

  const handleItemSubmit = async (itemData) => {
    try {
      let response;
      if (editingItem) {
        response = await itemsAPI.update(editingItem.id, itemData);
      } else {
        response = await itemsAPI.create(itemData);
      }
      
      // Handle image upload if present
      if (itemData.image && response.data.id) {
        const formData = new FormData();
        formData.append('image', itemData.image);
        await itemsAPI.uploadImage(response.data.id, formData);
      }
      
      await loadItems();
      setShowItemModal(false);
      setEditingItem(null);
    } catch (err) {
      setError('Failed to save item');
    }
  };

  const handleItemDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      try {
        await itemsAPI.delete(id);
        await loadItems();
      } catch (err) {
        setError('Failed to delete item');
      }
    }
  };

  const handleShowQRCode = async (tableId) => {
    try {
      const response = await qrCodeAPI.getQRCode(tableId);
      setQrCodeData(response.data);
      setShowQRModal(true);
    } catch (err) {
      setError('Failed to generate QR code');
    }
  };

  const getCategoryName = (categoryId) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : 'Unknown';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
            {error}
            <button 
              onClick={() => setError('')}
              className="float-right text-red-400 hover:text-red-600"
            >
              ×
            </button>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('categories')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'categories'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Categories
            </button>
            <button
              onClick={() => setActiveTab('items')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'items'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Menu Items
            </button>
            <button
              onClick={() => setActiveTab('qrcodes')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'qrcodes'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              QR Codes
            </button>
          </nav>
        </div>

        {/* Categories Tab */}
        {activeTab === 'categories' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Categories
                </h3>
                <button
                  onClick={() => setShowCategoryModal(true)}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Add Category
                </button>
              </div>
              
              {loading ? (
                <div className="text-center py-4">Loading...</div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Description
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {categories.map((category) => (
                        <tr key={category.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {category.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {category.description}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => {
                                setEditingCategory(category);
                                setShowCategoryModal(true);
                              }}
                              className="text-primary-600 hover:text-primary-900 mr-4"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleCategoryDelete(category.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Items Tab */}
        {activeTab === 'items' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Menu Items
                </h3>
                <button
                  onClick={() => setShowItemModal(true)}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Add Item
                </button>
              </div>
              
              {loading ? (
                <div className="text-center py-4">Loading...</div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {items.map((item) => (
                    <div key={item.id} className="border rounded-lg p-4">
                      {item.imageUrl && (
                        <img
                          src={item.imageUrl}
                          alt={item.name}
                          className="w-full h-32 object-cover rounded-md mb-3"
                        />
                      )}
                      <h4 className="font-medium text-gray-900">{item.name}</h4>
                      <p className="text-sm text-gray-500 mb-2">{item.description}</p>
                      <p className="text-lg font-semibold text-primary-600">${item.price}</p>
                      <p className="text-xs text-gray-400 mb-3">
                        Category: {getCategoryName(item.categoryId)}
                      </p>
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => {
                            setEditingItem(item);
                            setShowItemModal(true);
                          }}
                          className="text-primary-600 hover:text-primary-900 text-sm"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleItemDelete(item.id)}
                          className="text-red-600 hover:text-red-900 text-sm"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* QR Codes Tab */}
        {activeTab === 'qrcodes' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                QR Codes for Tables
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((tableId) => (
                  <div key={tableId} className="border rounded-lg p-4 text-center">
                    <h4 className="font-medium text-gray-900 mb-3">Table {tableId}</h4>
                    <button
                      onClick={() => handleShowQRCode(tableId)}
                      className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      View QR Code
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <Modal
        isOpen={showCategoryModal}
        onClose={() => {
          setShowCategoryModal(false);
          setEditingCategory(null);
        }}
        title={editingCategory ? 'Edit Category' : 'Add Category'}
      >
        <CategoryForm
          category={editingCategory}
          onSubmit={handleCategorySubmit}
          onCancel={() => {
            setShowCategoryModal(false);
            setEditingCategory(null);
          }}
        />
      </Modal>

      <Modal
        isOpen={showItemModal}
        onClose={() => {
          setShowItemModal(false);
          setEditingItem(null);
        }}
        title={editingItem ? 'Edit Item' : 'Add Item'}
      >
        <ItemForm
          item={editingItem}
          categories={categories}
          onSubmit={handleItemSubmit}
          onCancel={() => {
            setShowItemModal(false);
            setEditingItem(null);
          }}
        />
      </Modal>

      <Modal
        isOpen={showQRModal}
        onClose={() => {
          setShowQRModal(false);
          setQrCodeData(null);
        }}
        title="QR Code"
      >
        {qrCodeData && (
          <div className="text-center">
            <img
              src={qrCodeData.qrCodeUrl}
              alt="QR Code"
              className="mx-auto mb-4"
            />
            <p className="text-sm text-gray-600">
              Scan this QR code to view the menu for Table {qrCodeData.tableId}
            </p>
            <p className="text-xs text-gray-400 mt-2">
              URL: {qrCodeData.menuUrl}
            </p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Dashboard;
