import axios from 'axios';
import {
  mockAuthAPI,
  mockCategoriesAPI,
  mockItemsAPI,
  mockQrCodeAPI,
  useMockAPI
} from './mockApi';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/admin/login';
    }
    return Promise.reject(error);
  }
);

// Real API implementations
const realAuthAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => {
    localStorage.removeItem('token');
    return Promise.resolve();
  },
  isAuthenticated: () => !!localStorage.getItem('token'),
};

const realCategoriesAPI = {
  getAll: () => api.get('/categories'),
  create: (category) => api.post('/categories', category),
  update: (id, category) => api.put(`/categories/${id}`, category),
  delete: (id) => api.delete(`/categories/${id}`),
};

const realItemsAPI = {
  getAll: () => api.get('/items'),
  getByTableId: (tableId) => api.get(`/items?tableId=${tableId}`),
  create: (item) => api.post('/items', item),
  update: (id, item) => api.put(`/items/${id}`, item),
  delete: (id) => api.delete(`/items/${id}`),
  uploadImage: (id, formData) =>
    api.post(`/items/${id}/image`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
};

const realQrCodeAPI = {
  getQRCode: (tableId) => api.get(`/qrcode/${tableId}`),
};

// Export APIs (mock or real based on environment)
export const authAPI = useMockAPI ? mockAuthAPI : realAuthAPI;
export const categoriesAPI = useMockAPI ? mockCategoriesAPI : realCategoriesAPI;
export const itemsAPI = useMockAPI ? mockItemsAPI : realItemsAPI;
export const qrCodeAPI = useMockAPI ? mockQrCodeAPI : realQrCodeAPI;

export default api;
