{"ast": null, "code": "// Mock API for testing purposes\n// Replace this with actual API calls in production\n\nconst delay = ms => new Promise(resolve => setTimeout(resolve, ms));\n\n// Mock data\nlet mockCategories = [{\n  id: 1,\n  name: 'Appetizers',\n  description: 'Start your meal with our delicious appetizers'\n}, {\n  id: 2,\n  name: 'Main Courses',\n  description: 'Hearty and satisfying main dishes'\n}, {\n  id: 3,\n  name: 'Desser<PERSON>',\n  description: 'Sweet treats to end your meal'\n}, {\n  id: 4,\n  name: 'Beverages',\n  description: 'Refreshing drinks and beverages'\n}];\nlet mockItems = [{\n  id: 1,\n  name: 'Caesar Salad',\n  description: 'Fresh romaine lettuce with parmesan cheese and croutons',\n  price: 12.99,\n  categoryId: 1,\n  imageUrl: 'https://via.placeholder.com/300x200?text=Caesar+Salad'\n}, {\n  id: 2,\n  name: 'Grilled Salmon',\n  description: 'Fresh Atlantic salmon grilled to perfection',\n  price: 24.99,\n  categoryId: 2,\n  imageUrl: 'https://via.placeholder.com/300x200?text=Grilled+Salmon'\n}, {\n  id: 3,\n  name: 'Chocolate Cake',\n  description: 'Rich chocolate cake with vanilla ice cream',\n  price: 8.99,\n  categoryId: 3,\n  imageUrl: 'https://via.placeholder.com/300x200?text=Chocolate+Cake'\n}, {\n  id: 4,\n  name: 'Fresh Orange Juice',\n  description: 'Freshly squeezed orange juice',\n  price: 4.99,\n  categoryId: 4,\n  imageUrl: 'https://via.placeholder.com/300x200?text=Orange+Juice'\n}];\nlet nextId = 5;\n\n// Mock API functions\nexport const mockAuthAPI = {\n  login: async credentials => {\n    await delay(1000);\n    if (credentials.email === '<EMAIL>' && credentials.password === 'password') {\n      return {\n        data: {\n          token: 'mock-jwt-token-12345',\n          user: {\n            id: 1,\n            email: '<EMAIL>',\n            name: 'Admin'\n          }\n        }\n      };\n    } else {\n      throw new Error('Invalid credentials');\n    }\n  },\n  logout: async () => {\n    await delay(500);\n    return {\n      data: {\n        message: 'Logged out successfully'\n      }\n    };\n  },\n  isAuthenticated: () => !!localStorage.getItem('token')\n};\nexport const mockCategoriesAPI = {\n  getAll: async () => {\n    await delay(800);\n    return {\n      data: mockCategories\n    };\n  },\n  create: async category => {\n    await delay(1000);\n    const newCategory = {\n      ...category,\n      id: nextId++\n    };\n    mockCategories.push(newCategory);\n    return {\n      data: newCategory\n    };\n  },\n  update: async (id, category) => {\n    await delay(1000);\n    const index = mockCategories.findIndex(cat => cat.id === id);\n    if (index !== -1) {\n      mockCategories[index] = {\n        ...mockCategories[index],\n        ...category\n      };\n      return {\n        data: mockCategories[index]\n      };\n    }\n    throw new Error('Category not found');\n  },\n  delete: async id => {\n    await delay(800);\n    mockCategories = mockCategories.filter(cat => cat.id !== id);\n    return {\n      data: {\n        message: 'Category deleted'\n      }\n    };\n  }\n};\nexport const mockItemsAPI = {\n  getAll: async () => {\n    await delay(800);\n    return {\n      data: mockItems\n    };\n  },\n  getByTableId: async tableId => {\n    await delay(800);\n    // In a real app, this would filter by table\n    // For demo purposes, return all items\n    return {\n      data: mockItems\n    };\n  },\n  create: async item => {\n    await delay(1000);\n    const newItem = {\n      ...item,\n      id: nextId++\n    };\n    mockItems.push(newItem);\n    return {\n      data: newItem\n    };\n  },\n  update: async (id, item) => {\n    await delay(1000);\n    const index = mockItems.findIndex(itm => itm.id === id);\n    if (index !== -1) {\n      mockItems[index] = {\n        ...mockItems[index],\n        ...item\n      };\n      return {\n        data: mockItems[index]\n      };\n    }\n    throw new Error('Item not found');\n  },\n  delete: async id => {\n    await delay(800);\n    mockItems = mockItems.filter(itm => itm.id !== id);\n    return {\n      data: {\n        message: 'Item deleted'\n      }\n    };\n  },\n  uploadImage: async (id, formData) => {\n    await delay(1500);\n    // Mock image upload - in real app, this would upload to server\n    const mockImageUrl = `https://via.placeholder.com/300x200?text=Item+${id}`;\n    const index = mockItems.findIndex(itm => itm.id === id);\n    if (index !== -1) {\n      mockItems[index].imageUrl = mockImageUrl;\n      return {\n        data: {\n          imageUrl: mockImageUrl\n        }\n      };\n    }\n    throw new Error('Item not found');\n  }\n};\nexport const mockQrCodeAPI = {\n  getQRCode: async tableId => {\n    await delay(1000);\n    const menuUrl = `${window.location.origin}/menu/${tableId}`;\n    return {\n      data: {\n        tableId,\n        menuUrl,\n        qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(menuUrl)}`\n      }\n    };\n  }\n};\n\n// Export mock APIs for development\nexport const useMockAPI = process.env.NODE_ENV === 'development' && process.env.REACT_APP_USE_MOCK_API === 'true';", "map": {"version": 3, "names": ["delay", "ms", "Promise", "resolve", "setTimeout", "mockCategories", "id", "name", "description", "mockItems", "price", "categoryId", "imageUrl", "nextId", "mockAuthAPI", "login", "credentials", "email", "password", "data", "token", "user", "Error", "logout", "message", "isAuthenticated", "localStorage", "getItem", "mockCategoriesAPI", "getAll", "create", "category", "newCategory", "push", "update", "index", "findIndex", "cat", "delete", "filter", "mockItemsAPI", "getByTableId", "tableId", "item", "newItem", "itm", "uploadImage", "formData", "mockImageUrl", "mockQrCodeAPI", "getQRCode", "menuUrl", "window", "location", "origin", "qrCodeUrl", "encodeURIComponent", "useMockAPI", "process", "env", "NODE_ENV", "REACT_APP_USE_MOCK_API"], "sources": ["D:/Hotel/src/services/mockApi.js"], "sourcesContent": ["// Mock API for testing purposes\n// Replace this with actual API calls in production\n\nconst delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));\n\n// Mock data\nlet mockCategories = [\n  { id: 1, name: 'Appetizers', description: 'Start your meal with our delicious appetizers' },\n  { id: 2, name: 'Main Courses', description: 'Hearty and satisfying main dishes' },\n  { id: 3, name: 'Desser<PERSON>', description: 'Sweet treats to end your meal' },\n  { id: 4, name: 'Beverages', description: 'Refreshing drinks and beverages' },\n];\n\nlet mockItems = [\n  {\n    id: 1,\n    name: 'Caesar Salad',\n    description: 'Fresh romaine lettuce with parmesan cheese and croutons',\n    price: 12.99,\n    categoryId: 1,\n    imageUrl: 'https://via.placeholder.com/300x200?text=Caesar+Salad'\n  },\n  {\n    id: 2,\n    name: 'Grilled Salmon',\n    description: 'Fresh Atlantic salmon grilled to perfection',\n    price: 24.99,\n    categoryId: 2,\n    imageUrl: 'https://via.placeholder.com/300x200?text=Grilled+Salmon'\n  },\n  {\n    id: 3,\n    name: 'Chocolate Cake',\n    description: 'Rich chocolate cake with vanilla ice cream',\n    price: 8.99,\n    categoryId: 3,\n    imageUrl: 'https://via.placeholder.com/300x200?text=Chocolate+Cake'\n  },\n  {\n    id: 4,\n    name: 'Fresh Orange Juice',\n    description: 'Freshly squeezed orange juice',\n    price: 4.99,\n    categoryId: 4,\n    imageUrl: 'https://via.placeholder.com/300x200?text=Orange+Juice'\n  },\n];\n\nlet nextId = 5;\n\n// Mock API functions\nexport const mockAuthAPI = {\n  login: async (credentials) => {\n    await delay(1000);\n    if (credentials.email === '<EMAIL>' && credentials.password === 'password') {\n      return {\n        data: {\n          token: 'mock-jwt-token-12345',\n          user: { id: 1, email: '<EMAIL>', name: 'Admin' }\n        }\n      };\n    } else {\n      throw new Error('Invalid credentials');\n    }\n  },\n  logout: async () => {\n    await delay(500);\n    return { data: { message: 'Logged out successfully' } };\n  },\n  isAuthenticated: () => !!localStorage.getItem('token'),\n};\n\nexport const mockCategoriesAPI = {\n  getAll: async () => {\n    await delay(800);\n    return { data: mockCategories };\n  },\n  create: async (category) => {\n    await delay(1000);\n    const newCategory = { ...category, id: nextId++ };\n    mockCategories.push(newCategory);\n    return { data: newCategory };\n  },\n  update: async (id, category) => {\n    await delay(1000);\n    const index = mockCategories.findIndex(cat => cat.id === id);\n    if (index !== -1) {\n      mockCategories[index] = { ...mockCategories[index], ...category };\n      return { data: mockCategories[index] };\n    }\n    throw new Error('Category not found');\n  },\n  delete: async (id) => {\n    await delay(800);\n    mockCategories = mockCategories.filter(cat => cat.id !== id);\n    return { data: { message: 'Category deleted' } };\n  },\n};\n\nexport const mockItemsAPI = {\n  getAll: async () => {\n    await delay(800);\n    return { data: mockItems };\n  },\n  getByTableId: async (tableId) => {\n    await delay(800);\n    // In a real app, this would filter by table\n    // For demo purposes, return all items\n    return { data: mockItems };\n  },\n  create: async (item) => {\n    await delay(1000);\n    const newItem = { ...item, id: nextId++ };\n    mockItems.push(newItem);\n    return { data: newItem };\n  },\n  update: async (id, item) => {\n    await delay(1000);\n    const index = mockItems.findIndex(itm => itm.id === id);\n    if (index !== -1) {\n      mockItems[index] = { ...mockItems[index], ...item };\n      return { data: mockItems[index] };\n    }\n    throw new Error('Item not found');\n  },\n  delete: async (id) => {\n    await delay(800);\n    mockItems = mockItems.filter(itm => itm.id !== id);\n    return { data: { message: 'Item deleted' } };\n  },\n  uploadImage: async (id, formData) => {\n    await delay(1500);\n    // Mock image upload - in real app, this would upload to server\n    const mockImageUrl = `https://via.placeholder.com/300x200?text=Item+${id}`;\n    const index = mockItems.findIndex(itm => itm.id === id);\n    if (index !== -1) {\n      mockItems[index].imageUrl = mockImageUrl;\n      return { data: { imageUrl: mockImageUrl } };\n    }\n    throw new Error('Item not found');\n  },\n};\n\nexport const mockQrCodeAPI = {\n  getQRCode: async (tableId) => {\n    await delay(1000);\n    const menuUrl = `${window.location.origin}/menu/${tableId}`;\n    return {\n      data: {\n        tableId,\n        menuUrl,\n        qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(menuUrl)}`\n      }\n    };\n  },\n};\n\n// Export mock APIs for development\nexport const useMockAPI = process.env.NODE_ENV === 'development' && process.env.REACT_APP_USE_MOCK_API === 'true';\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,KAAK,GAAIC,EAAE,IAAK,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC,CAAC;;AAErE;AACA,IAAII,cAAc,GAAG,CACnB;EAAEC,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,YAAY;EAAEC,WAAW,EAAE;AAAgD,CAAC,EAC3F;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,cAAc;EAAEC,WAAW,EAAE;AAAoC,CAAC,EACjF;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,UAAU;EAAEC,WAAW,EAAE;AAAgC,CAAC,EACzE;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,WAAW;EAAEC,WAAW,EAAE;AAAkC,CAAC,CAC7E;AAED,IAAIC,SAAS,GAAG,CACd;EACEH,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,cAAc;EACpBC,WAAW,EAAE,yDAAyD;EACtEE,KAAK,EAAE,KAAK;EACZC,UAAU,EAAE,CAAC;EACbC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EAAE,6CAA6C;EAC1DE,KAAK,EAAE,KAAK;EACZC,UAAU,EAAE,CAAC;EACbC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EAAE,4CAA4C;EACzDE,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,CAAC;EACbC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,oBAAoB;EAC1BC,WAAW,EAAE,+BAA+B;EAC5CE,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,CAAC;EACbC,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,IAAIC,MAAM,GAAG,CAAC;;AAEd;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,MAAMhB,KAAK,CAAC,IAAI,CAAC;IACjB,IAAIgB,WAAW,CAACC,KAAK,KAAK,sBAAsB,IAAID,WAAW,CAACE,QAAQ,KAAK,UAAU,EAAE;MACvF,OAAO;QACLC,IAAI,EAAE;UACJC,KAAK,EAAE,sBAAsB;UAC7BC,IAAI,EAAE;YAAEf,EAAE,EAAE,CAAC;YAAEW,KAAK,EAAE,sBAAsB;YAAEV,IAAI,EAAE;UAAQ;QAC9D;MACF,CAAC;IACH,CAAC,MAAM;MACL,MAAM,IAAIe,KAAK,CAAC,qBAAqB,CAAC;IACxC;EACF,CAAC;EACDC,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,MAAMvB,KAAK,CAAC,GAAG,CAAC;IAChB,OAAO;MAAEmB,IAAI,EAAE;QAAEK,OAAO,EAAE;MAA0B;IAAE,CAAC;EACzD,CAAC;EACDC,eAAe,EAAEA,CAAA,KAAM,CAAC,CAACC,YAAY,CAACC,OAAO,CAAC,OAAO;AACvD,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAG;EAC/BC,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,MAAM7B,KAAK,CAAC,GAAG,CAAC;IAChB,OAAO;MAAEmB,IAAI,EAAEd;IAAe,CAAC;EACjC,CAAC;EACDyB,MAAM,EAAE,MAAOC,QAAQ,IAAK;IAC1B,MAAM/B,KAAK,CAAC,IAAI,CAAC;IACjB,MAAMgC,WAAW,GAAG;MAAE,GAAGD,QAAQ;MAAEzB,EAAE,EAAEO,MAAM;IAAG,CAAC;IACjDR,cAAc,CAAC4B,IAAI,CAACD,WAAW,CAAC;IAChC,OAAO;MAAEb,IAAI,EAAEa;IAAY,CAAC;EAC9B,CAAC;EACDE,MAAM,EAAE,MAAAA,CAAO5B,EAAE,EAAEyB,QAAQ,KAAK;IAC9B,MAAM/B,KAAK,CAAC,IAAI,CAAC;IACjB,MAAMmC,KAAK,GAAG9B,cAAc,CAAC+B,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAC/B,EAAE,KAAKA,EAAE,CAAC;IAC5D,IAAI6B,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB9B,cAAc,CAAC8B,KAAK,CAAC,GAAG;QAAE,GAAG9B,cAAc,CAAC8B,KAAK,CAAC;QAAE,GAAGJ;MAAS,CAAC;MACjE,OAAO;QAAEZ,IAAI,EAAEd,cAAc,CAAC8B,KAAK;MAAE,CAAC;IACxC;IACA,MAAM,IAAIb,KAAK,CAAC,oBAAoB,CAAC;EACvC,CAAC;EACDgB,MAAM,EAAE,MAAOhC,EAAE,IAAK;IACpB,MAAMN,KAAK,CAAC,GAAG,CAAC;IAChBK,cAAc,GAAGA,cAAc,CAACkC,MAAM,CAACF,GAAG,IAAIA,GAAG,CAAC/B,EAAE,KAAKA,EAAE,CAAC;IAC5D,OAAO;MAAEa,IAAI,EAAE;QAAEK,OAAO,EAAE;MAAmB;IAAE,CAAC;EAClD;AACF,CAAC;AAED,OAAO,MAAMgB,YAAY,GAAG;EAC1BX,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,MAAM7B,KAAK,CAAC,GAAG,CAAC;IAChB,OAAO;MAAEmB,IAAI,EAAEV;IAAU,CAAC;EAC5B,CAAC;EACDgC,YAAY,EAAE,MAAOC,OAAO,IAAK;IAC/B,MAAM1C,KAAK,CAAC,GAAG,CAAC;IAChB;IACA;IACA,OAAO;MAAEmB,IAAI,EAAEV;IAAU,CAAC;EAC5B,CAAC;EACDqB,MAAM,EAAE,MAAOa,IAAI,IAAK;IACtB,MAAM3C,KAAK,CAAC,IAAI,CAAC;IACjB,MAAM4C,OAAO,GAAG;MAAE,GAAGD,IAAI;MAAErC,EAAE,EAAEO,MAAM;IAAG,CAAC;IACzCJ,SAAS,CAACwB,IAAI,CAACW,OAAO,CAAC;IACvB,OAAO;MAAEzB,IAAI,EAAEyB;IAAQ,CAAC;EAC1B,CAAC;EACDV,MAAM,EAAE,MAAAA,CAAO5B,EAAE,EAAEqC,IAAI,KAAK;IAC1B,MAAM3C,KAAK,CAAC,IAAI,CAAC;IACjB,MAAMmC,KAAK,GAAG1B,SAAS,CAAC2B,SAAS,CAACS,GAAG,IAAIA,GAAG,CAACvC,EAAE,KAAKA,EAAE,CAAC;IACvD,IAAI6B,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB1B,SAAS,CAAC0B,KAAK,CAAC,GAAG;QAAE,GAAG1B,SAAS,CAAC0B,KAAK,CAAC;QAAE,GAAGQ;MAAK,CAAC;MACnD,OAAO;QAAExB,IAAI,EAAEV,SAAS,CAAC0B,KAAK;MAAE,CAAC;IACnC;IACA,MAAM,IAAIb,KAAK,CAAC,gBAAgB,CAAC;EACnC,CAAC;EACDgB,MAAM,EAAE,MAAOhC,EAAE,IAAK;IACpB,MAAMN,KAAK,CAAC,GAAG,CAAC;IAChBS,SAAS,GAAGA,SAAS,CAAC8B,MAAM,CAACM,GAAG,IAAIA,GAAG,CAACvC,EAAE,KAAKA,EAAE,CAAC;IAClD,OAAO;MAAEa,IAAI,EAAE;QAAEK,OAAO,EAAE;MAAe;IAAE,CAAC;EAC9C,CAAC;EACDsB,WAAW,EAAE,MAAAA,CAAOxC,EAAE,EAAEyC,QAAQ,KAAK;IACnC,MAAM/C,KAAK,CAAC,IAAI,CAAC;IACjB;IACA,MAAMgD,YAAY,GAAG,iDAAiD1C,EAAE,EAAE;IAC1E,MAAM6B,KAAK,GAAG1B,SAAS,CAAC2B,SAAS,CAACS,GAAG,IAAIA,GAAG,CAACvC,EAAE,KAAKA,EAAE,CAAC;IACvD,IAAI6B,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB1B,SAAS,CAAC0B,KAAK,CAAC,CAACvB,QAAQ,GAAGoC,YAAY;MACxC,OAAO;QAAE7B,IAAI,EAAE;UAAEP,QAAQ,EAAEoC;QAAa;MAAE,CAAC;IAC7C;IACA,MAAM,IAAI1B,KAAK,CAAC,gBAAgB,CAAC;EACnC;AACF,CAAC;AAED,OAAO,MAAM2B,aAAa,GAAG;EAC3BC,SAAS,EAAE,MAAOR,OAAO,IAAK;IAC5B,MAAM1C,KAAK,CAAC,IAAI,CAAC;IACjB,MAAMmD,OAAO,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,SAASZ,OAAO,EAAE;IAC3D,OAAO;MACLvB,IAAI,EAAE;QACJuB,OAAO;QACPS,OAAO;QACPI,SAAS,EAAE,iEAAiEC,kBAAkB,CAACL,OAAO,CAAC;MACzG;IACF,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,UAAU,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIF,OAAO,CAACC,GAAG,CAACE,sBAAsB,KAAK,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}