# Restaurant QR Code Menu Catalogue

A modern React web application for managing restaurant menus with QR code integration for table-specific menu access.

## Features

### Admin Panel
- **Authentication**: Secure login with JWT token management
- **Category Management**: Create, edit, and delete menu categories
- **Menu Item Management**: Full CRUD operations for menu items with image upload
- **QR Code Generation**: Generate QR codes for each table
- **Responsive Design**: Modern UI with Tailwind CSS

### Public Menu
- **Table-Specific Menus**: Access menu items by scanning table QR codes
- **Category Filtering**: Filter items by category
- **Responsive Layout**: Optimized for mobile and desktop viewing
- **Image Display**: Show item images when available

## Tech Stack

- **Frontend**: React 18, React Router v6
- **Styling**: Tailwind CSS
- **HTTP Client**: Axios
- **State Management**: React Hooks (useState, useEffect, useContext)

## Project Structure

```
src/
├── components/          # Reusable components
│   ├── CategoryForm.js  # Category creation/editing form
│   ├── ItemForm.js      # Menu item creation/editing form
│   ├── Modal.js         # Modal component
│   ├── Navbar.js        # Navigation bar
│   └── ProtectedRoute.js # Route protection wrapper
├── contexts/            # React contexts
│   └── AuthContext.js   # Authentication context
├── pages/               # Page components
│   ├── Dashboard.js     # Admin dashboard
│   ├── LoginPage.js     # Admin login
│   └── MenuPage.js      # Public menu display
├── services/            # API services
│   └── api.js           # Centralized API client
├── App.js               # Main app component
├── index.js             # App entry point
└── index.css            # Global styles
```

## API Endpoints

The application expects the following API endpoints:

### Authentication
- `POST /api/auth/login` - Admin login

### Categories
- `GET /api/categories` - Get all categories
- `POST /api/categories` - Create category
- `PUT /api/categories/:id` - Update category
- `DELETE /api/categories/:id` - Delete category

### Menu Items
- `GET /api/items` - Get all items
- `GET /api/items?tableId={id}` - Get items by table ID
- `POST /api/items` - Create item
- `PUT /api/items/:id` - Update item
- `DELETE /api/items/:id` - Delete item
- `POST /api/items/:id/image` - Upload item image

### QR Codes
- `GET /api/qrcode/:tableId` - Get QR code for table

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

4. Update the API URL in `.env`:
   ```
   REACT_APP_API_URL=http://your-api-server:port/api
   ```

5. Start the development server:
   ```bash
   npm start
   ```

## Usage

### Admin Access
1. Navigate to `/admin/login`
2. Login with admin credentials
3. Access the dashboard at `/admin/dashboard`

### Public Menu Access
- Navigate to `/menu/{tableId}` where `{tableId}` is the table number
- Or scan the QR code generated for each table

## Routes

- `/admin/login` - Admin login page
- `/admin/dashboard` - Admin dashboard (protected)
- `/menu/:tableId` - Public menu page for specific table
- `/` - Redirects to admin login

## Development

### Available Scripts

- `npm start` - Start development server
- `npm build` - Build for production
- `npm test` - Run tests
- `npm eject` - Eject from Create React App

### Customization

#### Styling
The application uses Tailwind CSS with a custom color palette. Modify `tailwind.config.js` to customize colors and styling.

#### API Integration
Update `src/services/api.js` to modify API endpoints or add new API functions.

#### Authentication
The app uses JWT tokens stored in localStorage. Modify `src/contexts/AuthContext.js` to change authentication behavior.

## Production Deployment

1. Build the application:
   ```bash
   npm run build
   ```

2. Deploy the `build` folder to your web server

3. Configure your web server to serve the React app and handle client-side routing

4. Ensure your API server is accessible from the production domain

## License

This project is licensed under the MIT License.
