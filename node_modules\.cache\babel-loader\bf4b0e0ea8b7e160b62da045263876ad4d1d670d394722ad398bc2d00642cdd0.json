{"ast": null, "code": "var _jsxFileName = \"D:\\\\Hotel\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const {\n    logout\n  } = useAuth();\n  const handleLogout = async () => {\n    await logout();\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-white shadow-lg\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-primary-600\",\n              children: \"Restaurant Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-700\",\n            children: \"Admin Panel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleLogout,\n            className: \"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors\",\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"JN45pPWTCN0QEZFRvGkjOxroHA0=\", false, function () {\n  return [useAuth];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "logout", "handleLogout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Hotel/src/components/Navbar.js"], "sourcesContent": ["import React from 'react';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Navbar = () => {\n  const { logout } = useAuth();\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <h1 className=\"text-xl font-bold text-primary-600\">\n                Restaurant Admin\n              </h1>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            <span className=\"text-gray-700\">Admin Panel</span>\n            <button\n              onClick={handleLogout}\n              className=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAO,CAAC,GAAGL,OAAO,CAAC,CAAC;EAE5B,MAAMM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMD,MAAM,CAAC,CAAC;EAChB,CAAC;EAED,oBACEH,OAAA;IAAKK,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjCN,OAAA;MAAKK,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDN,OAAA;QAAKK,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCN,OAAA;UAAKK,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCN,OAAA;YAAKK,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BN,OAAA;cAAIK,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKK,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CN,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDV,OAAA;YACEW,OAAO,EAAEP,YAAa;YACtBC,SAAS,EAAC,2GAA2G;YAAAC,QAAA,EACtH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACR,EAAA,CAhCID,MAAM;EAAA,QACSH,OAAO;AAAA;AAAAc,EAAA,GADtBX,MAAM;AAkCZ,eAAeA,MAAM;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}