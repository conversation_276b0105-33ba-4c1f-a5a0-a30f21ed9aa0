{"ast": null, "code": "var _jsxFileName = \"D:\\\\Hotel\\\\src\\\\components\\\\CategoryForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryForm = ({\n  category,\n  onSubmit,\n  onCancel\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    description: ''\n  });\n  const [errors, setErrors] = useState({});\n  useEffect(() => {\n    if (category) {\n      setFormData({\n        name: category.name || '',\n        description: category.description || ''\n      });\n    }\n  }, [category]);\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Category name is required';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (validateForm()) {\n      onSubmit(formData);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    className: \"space-y-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"name\",\n        className: \"block text-sm font-medium text-gray-700\",\n        children: \"Category Name *\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: \"name\",\n        name: \"name\",\n        value: formData.name,\n        onChange: handleChange,\n        className: `mt-1 block w-full px-3 py-2 border ${errors.name ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500`,\n        placeholder: \"Enter category name\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-red-600\",\n        children: errors.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"description\",\n        className: \"block text-sm font-medium text-gray-700\",\n        children: \"Description\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        id: \"description\",\n        name: \"description\",\n        rows: 3,\n        value: formData.description,\n        onChange: handleChange,\n        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n        placeholder: \"Enter category description\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end space-x-3 pt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: onCancel,\n        className: \"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n        children: [category ? 'Update' : 'Create', \" Category\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryForm, \"scwfU5290xIELoj2tA8g3FKtGUg=\");\n_c = CategoryForm;\nexport default CategoryForm;\nvar _c;\n$RefreshReg$(_c, \"CategoryForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "CategoryForm", "category", "onSubmit", "onCancel", "_s", "formData", "setFormData", "name", "description", "errors", "setErrors", "validateForm", "newErrors", "trim", "Object", "keys", "length", "handleSubmit", "e", "preventDefault", "handleChange", "value", "target", "prev", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "id", "onChange", "placeholder", "rows", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Hotel/src/components/CategoryForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst CategoryForm = ({ category, onSubmit, onCancel }) => {\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n  });\n  const [errors, setErrors] = useState({});\n\n  useEffect(() => {\n    if (category) {\n      setFormData({\n        name: category.name || '',\n        description: category.description || '',\n      });\n    }\n  }, [category]);\n\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.name.trim()) {\n      newErrors.name = 'Category name is required';\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (validateForm()) {\n      onSubmit(formData);\n    }\n  };\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      <div>\n        <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n          Category Name *\n        </label>\n        <input\n          type=\"text\"\n          id=\"name\"\n          name=\"name\"\n          value={formData.name}\n          onChange={handleChange}\n          className={`mt-1 block w-full px-3 py-2 border ${\n            errors.name ? 'border-red-300' : 'border-gray-300'\n          } rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500`}\n          placeholder=\"Enter category name\"\n        />\n        {errors.name && (\n          <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>\n        )}\n      </div>\n\n      <div>\n        <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\n          Description\n        </label>\n        <textarea\n          id=\"description\"\n          name=\"description\"\n          rows={3}\n          value={formData.description}\n          onChange={handleChange}\n          className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n          placeholder=\"Enter category description\"\n        />\n      </div>\n\n      <div className=\"flex justify-end space-x-3 pt-4\">\n        <button\n          type=\"button\"\n          onClick={onCancel}\n          className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n        >\n          Cancel\n        </button>\n        <button\n          type=\"submit\"\n          className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n        >\n          {category ? 'Update' : 'Create'} Category\n        </button>\n      </div>\n    </form>\n  );\n};\n\nexport default CategoryForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd,IAAII,QAAQ,EAAE;MACZK,WAAW,CAAC;QACVC,IAAI,EAAEN,QAAQ,CAACM,IAAI,IAAI,EAAE;QACzBC,WAAW,EAAEP,QAAQ,CAACO,WAAW,IAAI;MACvC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACP,QAAQ,CAACE,IAAI,CAACM,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACL,IAAI,GAAG,2BAA2B;IAC9C;IAEAG,SAAS,CAACE,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIR,YAAY,CAAC,CAAC,EAAE;MAClBT,QAAQ,CAACG,QAAQ,CAAC;IACpB;EACF,CAAC;EAED,MAAMe,YAAY,GAAIF,CAAC,IAAK;IAC1B,MAAM;MAAEX,IAAI;MAAEc;IAAM,CAAC,GAAGH,CAAC,CAACI,MAAM;IAChChB,WAAW,CAACiB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAChB,IAAI,GAAGc;IACV,CAAC,CAAC,CAAC;IAEH,IAAIZ,MAAM,CAACF,IAAI,CAAC,EAAE;MAChBG,SAAS,CAACa,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAAChB,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,oBACER,OAAA;IAAMG,QAAQ,EAAEe,YAAa;IAACO,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACjD1B,OAAA;MAAA0B,QAAA,gBACE1B,OAAA;QAAO2B,OAAO,EAAC,MAAM;QAACF,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EAAC;MAE1E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR/B,OAAA;QACEgC,IAAI,EAAC,MAAM;QACXC,EAAE,EAAC,MAAM;QACTzB,IAAI,EAAC,MAAM;QACXc,KAAK,EAAEhB,QAAQ,CAACE,IAAK;QACrB0B,QAAQ,EAAEb,YAAa;QACvBI,SAAS,EAAE,sCACTf,MAAM,CAACF,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,0FACuC;QAC3F2B,WAAW,EAAC;MAAqB;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,EACDrB,MAAM,CAACF,IAAI,iBACVR,OAAA;QAAGyB,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAEhB,MAAM,CAACF;MAAI;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAC1D;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN/B,OAAA;MAAA0B,QAAA,gBACE1B,OAAA;QAAO2B,OAAO,EAAC,aAAa;QAACF,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EAAC;MAEjF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR/B,OAAA;QACEiC,EAAE,EAAC,aAAa;QAChBzB,IAAI,EAAC,aAAa;QAClB4B,IAAI,EAAE,CAAE;QACRd,KAAK,EAAEhB,QAAQ,CAACG,WAAY;QAC5ByB,QAAQ,EAAEb,YAAa;QACvBI,SAAS,EAAC,4IAA4I;QACtJU,WAAW,EAAC;MAA4B;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN/B,OAAA;MAAKyB,SAAS,EAAC,iCAAiC;MAAAC,QAAA,gBAC9C1B,OAAA;QACEgC,IAAI,EAAC,QAAQ;QACbK,OAAO,EAAEjC,QAAS;QAClBqB,SAAS,EAAC,2KAA2K;QAAAC,QAAA,EACtL;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/B,OAAA;QACEgC,IAAI,EAAC,QAAQ;QACbP,SAAS,EAAC,wMAAwM;QAAAC,QAAA,GAEjNxB,QAAQ,GAAG,QAAQ,GAAG,QAAQ,EAAC,WAClC;MAAA;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAAC1B,EAAA,CAvGIJ,YAAY;AAAAqC,EAAA,GAAZrC,YAAY;AAyGlB,eAAeA,YAAY;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}