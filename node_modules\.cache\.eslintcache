[{"D:\\Hotel\\src\\index.js": "1", "D:\\Hotel\\src\\App.js": "2", "D:\\Hotel\\src\\components\\ProtectedRoute.js": "3", "D:\\Hotel\\src\\pages\\MenuPage.js": "4", "D:\\Hotel\\src\\pages\\LoginPage.js": "5", "D:\\Hotel\\src\\pages\\Dashboard.js": "6", "D:\\Hotel\\src\\contexts\\AuthContext.js": "7", "D:\\Hotel\\src\\components\\Modal.js": "8", "D:\\Hotel\\src\\components\\CategoryForm.js": "9", "D:\\Hotel\\src\\components\\Navbar.js": "10", "D:\\Hotel\\src\\components\\ItemForm.js": "11", "D:\\Hotel\\src\\services\\api.js": "12", "D:\\Hotel\\src\\services\\mockApi.js": "13"}, {"size": 254, "mtime": 1755156177311, "results": "14", "hashOfConfig": "15"}, {"size": 1924, "mtime": 1755156172299, "results": "16", "hashOfConfig": "15"}, {"size": 554, "mtime": 1755155988898, "results": "17", "hashOfConfig": "15"}, {"size": 6711, "mtime": 1755156155480, "results": "18", "hashOfConfig": "15"}, {"size": 4854, "mtime": 1755156008537, "results": "19", "hashOfConfig": "15"}, {"size": 14094, "mtime": 1755156122592, "results": "20", "hashOfConfig": "15"}, {"size": 1380, "mtime": 1755155983080, "results": "21", "hashOfConfig": "15"}, {"size": 957, "mtime": 1755156030122, "results": "22", "hashOfConfig": "15"}, {"size": 3099, "mtime": 1755156044273, "results": "23", "hashOfConfig": "15"}, {"size": 1074, "mtime": 1755156022468, "results": "24", "hashOfConfig": "15"}, {"size": 6518, "mtime": 1755156069618, "results": "25", "hashOfConfig": "15"}, {"size": 2314, "mtime": 1755156260493, "results": "26", "hashOfConfig": "15"}, {"size": 4735, "mtime": 1755156239248, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "toqk4g", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Hotel\\src\\index.js", [], [], "D:\\Hotel\\src\\App.js", [], [], "D:\\Hotel\\src\\components\\ProtectedRoute.js", [], [], "D:\\Hotel\\src\\pages\\MenuPage.js", ["67"], [], "D:\\Hotel\\src\\pages\\LoginPage.js", [], [], "D:\\Hotel\\src\\pages\\Dashboard.js", [], [], "D:\\Hotel\\src\\contexts\\AuthContext.js", [], [], "D:\\Hotel\\src\\components\\Modal.js", [], [], "D:\\Hotel\\src\\components\\CategoryForm.js", [], [], "D:\\Hotel\\src\\components\\Navbar.js", [], [], "D:\\Hotel\\src\\components\\ItemForm.js", [], [], "D:\\Hotel\\src\\services\\api.js", [], [], "D:\\Hotel\\src\\services\\mockApi.js", [], [], {"ruleId": "68", "severity": 1, "message": "69", "line": 15, "column": 6, "nodeType": "70", "endLine": 15, "endColumn": 15, "suggestions": "71"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadMenuData'. Either include it or remove the dependency array.", "ArrayExpression", ["72"], {"desc": "73", "fix": "74"}, "Update the dependencies array to be: [loadMenuData, tableId]", {"range": "75", "text": "76"}, [515, 524], "[loadMenuData, tableId]"]