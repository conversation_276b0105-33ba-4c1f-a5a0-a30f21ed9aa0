import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { itemsAPI, categoriesAPI } from '../services/api';

const MenuPage = () => {
  const { tableId } = useParams();
  const [items, setItems] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    loadMenuData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableId]);

  const loadMenuData = async () => {
    try {
      setLoading(true);
      const [itemsResponse, categoriesResponse] = await Promise.all([
        itemsAPI.getByTableId(tableId),
        categoriesAPI.getAll()
      ]);
      
      setItems(itemsResponse.data);
      setCategories(categoriesResponse.data);
    } catch (err) {
      setError('Failed to load menu. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getCategoryName = (categoryId) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : 'Other';
  };

  const groupItemsByCategory = () => {
    const grouped = {};
    
    items.forEach(item => {
      const categoryName = getCategoryName(item.categoryId);
      if (!grouped[categoryName]) {
        grouped[categoryName] = [];
      }
      grouped[categoryName].push(item);
    });
    
    return grouped;
  };

  const filteredItems = selectedCategory === 'all' 
    ? groupItemsByCategory()
    : { [selectedCategory]: groupItemsByCategory()[selectedCategory] || [] };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">{error}</div>
          <button
            onClick={loadMenuData}
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900">Our Menu</h1>
            <p className="text-gray-600 mt-2">Table {tableId}</p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Category Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap justify-center gap-2">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === 'all'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100'
              }`}
            >
              All Items
            </button>
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.name)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === category.name
                    ? 'bg-primary-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Menu Items */}
        {Object.keys(filteredItems).length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No menu items available.</p>
          </div>
        ) : (
          <div className="space-y-8">
            {Object.entries(filteredItems).map(([categoryName, categoryItems]) => (
              <div key={categoryName} className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="bg-primary-50 px-6 py-4 border-b border-primary-100">
                  <h2 className="text-xl font-semibold text-primary-800">{categoryName}</h2>
                </div>
                
                <div className="p-6">
                  <div className="grid gap-6">
                    {categoryItems.map(item => (
                      <div key={item.id} className="flex flex-col sm:flex-row gap-4 p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        {item.imageUrl && (
                          <div className="flex-shrink-0">
                            <img
                              src={item.imageUrl}
                              alt={item.name}
                              className="w-full sm:w-24 h-24 object-cover rounded-lg"
                            />
                          </div>
                        )}
                        
                        <div className="flex-grow">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="text-lg font-medium text-gray-900">{item.name}</h3>
                              {item.description && (
                                <p className="text-gray-600 mt-1 text-sm">{item.description}</p>
                              )}
                            </div>
                            <div className="ml-4 flex-shrink-0">
                              <span className="text-xl font-bold text-primary-600">
                                ${item.price.toFixed(2)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-4xl mx-auto px-4 py-6 text-center">
          <p className="text-gray-500 text-sm">
            Thank you for dining with us! 
          </p>
          <p className="text-gray-400 text-xs mt-1">
            Scan the QR code on your table to view this menu anytime.
          </p>
        </div>
      </div>
    </div>
  );
};

export default MenuPage;
