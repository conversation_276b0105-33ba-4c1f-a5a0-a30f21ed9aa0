{"ast": null, "code": "var _jsxFileName = \"D:\\\\Hotel\\\\src\\\\pages\\\\LoginPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const {\n    login,\n    isAuthenticated\n  } = useAuth();\n\n  // Redirect if already authenticated\n  if (isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/admin/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 12\n    }, this);\n  }\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    setLoading(true);\n    const result = await login(formData);\n    if (!result.success) {\n      setErrors({\n        general: result.error\n      });\n    }\n    setLoading(false);\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n          children: \"Admin Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-center text-sm text-gray-600\",\n          children: \"Sign in to manage your restaurant menu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"mt-8 space-y-6\",\n        onSubmit: handleSubmit,\n        children: [errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded\",\n          children: errors.general\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Email Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"email\",\n              name: \"email\",\n              type: \"email\",\n              autoComplete: \"email\",\n              value: formData.email,\n              onChange: handleChange,\n              className: `mt-1 appearance-none relative block w-full px-3 py-2 border ${errors.email ? 'border-red-300' : 'border-gray-300'} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm`,\n              placeholder: \"Enter your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"password\",\n              name: \"password\",\n              type: \"password\",\n              autoComplete: \"current-password\",\n              value: formData.password,\n              onChange: handleChange,\n              className: `mt-1 appearance-none relative block w-full px-3 py-2 border ${errors.password ? 'border-red-300' : 'border-gray-300'} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm`,\n              placeholder: \"Enter your password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.password\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: loading ? 'Signing in...' : 'Sign in'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"HIY0pgtIlNiAdJHTe56jo9X4KVE=\", false, function () {\n  return [useAuth];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "Navigate", "useAuth", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "formData", "setFormData", "email", "password", "errors", "setErrors", "loading", "setLoading", "login", "isAuthenticated", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "validateForm", "newErrors", "test", "length", "Object", "keys", "handleSubmit", "e", "preventDefault", "result", "success", "general", "error", "handleChange", "name", "value", "target", "prev", "className", "children", "onSubmit", "htmlFor", "id", "type", "autoComplete", "onChange", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Hotel/src/pages/LoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst LoginPage = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const { login, isAuthenticated } = useAuth();\n\n  // Redirect if already authenticated\n  if (isAuthenticated) {\n    return <Navigate to=\"/admin/dashboard\" replace />;\n  }\n\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    \n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n    \n    setLoading(true);\n    const result = await login(formData);\n    \n    if (!result.success) {\n      setErrors({ general: result.error });\n    }\n    \n    setLoading(false);\n  };\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Admin Login\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Sign in to manage your restaurant menu\n          </p>\n        </div>\n        \n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          {errors.general && (\n            <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded\">\n              {errors.general}\n            </div>\n          )}\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email Address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                value={formData.email}\n                onChange={handleChange}\n                className={`mt-1 appearance-none relative block w-full px-3 py-2 border ${\n                  errors.email ? 'border-red-300' : 'border-gray-300'\n                } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm`}\n                placeholder=\"Enter your email\"\n              />\n              {errors.email && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>\n              )}\n            </div>\n            \n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                autoComplete=\"current-password\"\n                value={formData.password}\n                onChange={handleChange}\n                className={`mt-1 appearance-none relative block w-full px-3 py-2 border ${\n                  errors.password ? 'border-red-300' : 'border-gray-300'\n                } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm`}\n                placeholder=\"Enter your password\"\n              />\n              {errors.password && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.password}</p>\n              )}\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? 'Signing in...' : 'Sign in'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC;IACvCS,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEe,KAAK;IAAEC;EAAgB,CAAC,GAAGd,OAAO,CAAC,CAAC;;EAE5C;EACA,IAAIc,eAAe,EAAE;IACnB,oBAAOZ,OAAA,CAACH,QAAQ;MAACgB,EAAE,EAAC,kBAAkB;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnD;EAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACjB,QAAQ,CAACE,KAAK,EAAE;MACnBe,SAAS,CAACf,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACgB,IAAI,CAAClB,QAAQ,CAACE,KAAK,CAAC,EAAE;MAC/Ce,SAAS,CAACf,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBc,SAAS,CAACd,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIH,QAAQ,CAACG,QAAQ,CAACgB,MAAM,GAAG,CAAC,EAAE;MACvCF,SAAS,CAACd,QAAQ,GAAG,wCAAwC;IAC/D;IAEAE,SAAS,CAACY,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;IAErBT,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMkB,MAAM,GAAG,MAAMjB,KAAK,CAACR,QAAQ,CAAC;IAEpC,IAAI,CAACyB,MAAM,CAACC,OAAO,EAAE;MACnBrB,SAAS,CAAC;QAAEsB,OAAO,EAAEF,MAAM,CAACG;MAAM,CAAC,CAAC;IACtC;IAEArB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMsB,YAAY,GAAIN,CAAC,IAAK;IAC1B,MAAM;MAAEO,IAAI;MAAEC;IAAM,CAAC,GAAGR,CAAC,CAACS,MAAM;IAChC/B,WAAW,CAACgC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAI3B,MAAM,CAAC0B,IAAI,CAAC,EAAE;MAChBzB,SAAS,CAAC4B,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,oBACEjC,OAAA;IAAKqC,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAClGtC,OAAA;MAAKqC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxCtC,OAAA;QAAAsC,QAAA,gBACEtC,OAAA;UAAIqC,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEvE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlB,OAAA;UAAGqC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEtD;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENlB,OAAA;QAAMqC,SAAS,EAAC,gBAAgB;QAACE,QAAQ,EAAEd,YAAa;QAAAa,QAAA,GACrD/B,MAAM,CAACuB,OAAO,iBACb9B,OAAA;UAAKqC,SAAS,EAAC,gEAAgE;UAAAC,QAAA,EAC5E/B,MAAM,CAACuB;QAAO;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CACN,eAEDlB,OAAA;UAAKqC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtC,OAAA;YAAAsC,QAAA,gBACEtC,OAAA;cAAOwC,OAAO,EAAC,OAAO;cAACH,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlB,OAAA;cACEyC,EAAE,EAAC,OAAO;cACVR,IAAI,EAAC,OAAO;cACZS,IAAI,EAAC,OAAO;cACZC,YAAY,EAAC,OAAO;cACpBT,KAAK,EAAE/B,QAAQ,CAACE,KAAM;cACtBuC,QAAQ,EAAEZ,YAAa;cACvBK,SAAS,EAAE,+DACT9B,MAAM,CAACF,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,yIACqF;cAC1IwC,WAAW,EAAC;YAAkB;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,EACDX,MAAM,CAACF,KAAK,iBACXL,OAAA;cAAGqC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE/B,MAAM,CAACF;YAAK;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC3D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENlB,OAAA;YAAAsC,QAAA,gBACEtC,OAAA;cAAOwC,OAAO,EAAC,UAAU;cAACH,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlB,OAAA;cACEyC,EAAE,EAAC,UAAU;cACbR,IAAI,EAAC,UAAU;cACfS,IAAI,EAAC,UAAU;cACfC,YAAY,EAAC,kBAAkB;cAC/BT,KAAK,EAAE/B,QAAQ,CAACG,QAAS;cACzBsC,QAAQ,EAAEZ,YAAa;cACvBK,SAAS,EAAE,+DACT9B,MAAM,CAACD,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB,yIACkF;cAC1IuC,WAAW,EAAC;YAAqB;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,EACDX,MAAM,CAACD,QAAQ,iBACdN,OAAA;cAAGqC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE/B,MAAM,CAACD;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC9D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlB,OAAA;UAAAsC,QAAA,eACEtC,OAAA;YACE0C,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAErC,OAAQ;YAClB4B,SAAS,EAAC,wRAAwR;YAAAC,QAAA,EAEjS7B,OAAO,GAAG,eAAe,GAAG;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CA5IID,SAAS;EAAA,QAOsBH,OAAO;AAAA;AAAAiD,EAAA,GAPtC9C,SAAS;AA8If,eAAeA,SAAS;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}