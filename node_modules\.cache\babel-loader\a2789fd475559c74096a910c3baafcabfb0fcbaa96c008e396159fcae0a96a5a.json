{"ast": null, "code": "var _jsxFileName = \"D:\\\\Hotel\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Check if user is authenticated on app load\n    const token = localStorage.getItem('token');\n    setIsAuthenticated(!!token);\n    setLoading(false);\n  }, []);\n  const login = async credentials => {\n    try {\n      const response = await authAPI.login(credentials);\n      const {\n        token\n      } = response.data;\n      localStorage.setItem('token', token);\n      setIsAuthenticated(true);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      return {\n        success: false,\n        error: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed'\n      };\n    }\n  };\n  const logout = async () => {\n    await authAPI.logout();\n    setIsAuthenticated(false);\n  };\n  const value = {\n    isAuthenticated,\n    login,\n    logout,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"Ax+B/BOELR06clzL32vENhaokmk=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "token", "localStorage", "getItem", "login", "credentials", "response", "data", "setItem", "success", "error", "_error$response", "_error$response$data", "message", "logout", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Hotel/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is authenticated on app load\n    const token = localStorage.getItem('token');\n    setIsAuthenticated(!!token);\n    setLoading(false);\n  }, []);\n\n  const login = async (credentials) => {\n    try {\n      const response = await authAPI.login(credentials);\n      const { token } = response.data;\n      localStorage.setItem('token', token);\n      setIsAuthenticated(true);\n      return { success: true };\n    } catch (error) {\n      return { \n        success: false, \n        error: error.response?.data?.message || 'Login failed' \n      };\n    }\n  };\n\n  const logout = async () => {\n    await authAPI.logout();\n    setIsAuthenticated(false);\n  };\n\n  const value = {\n    isAuthenticated,\n    login,\n    logout,\n    loading,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3CL,kBAAkB,CAAC,CAAC,CAACG,KAAK,CAAC;IAC3BD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpB,OAAO,CAACkB,KAAK,CAACC,WAAW,CAAC;MACjD,MAAM;QAAEJ;MAAM,CAAC,GAAGK,QAAQ,CAACC,IAAI;MAC/BL,YAAY,CAACM,OAAO,CAAC,OAAO,EAAEP,KAAK,CAAC;MACpCH,kBAAkB,CAAC,IAAI,CAAC;MACxB,OAAO;QAAEW,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACd,OAAO;QACLH,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE,EAAAC,eAAA,GAAAD,KAAK,CAACJ,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBJ,IAAI,cAAAK,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI;MAC1C,CAAC;IACH;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,MAAM5B,OAAO,CAAC4B,MAAM,CAAC,CAAC;IACtBhB,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMiB,KAAK,GAAG;IACZlB,eAAe;IACfO,KAAK;IACLU,MAAM;IACNf;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,WAAW,CAAC2B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAApB,QAAA,EAChCA;EAAQ;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACxB,GAAA,CA3CWF,YAAY;AAAA2B,EAAA,GAAZ3B,YAAY;AAAA,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}