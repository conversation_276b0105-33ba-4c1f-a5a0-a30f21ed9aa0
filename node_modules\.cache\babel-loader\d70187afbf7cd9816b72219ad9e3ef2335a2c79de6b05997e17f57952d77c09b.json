{"ast": null, "code": "import axios from 'axios';\nimport { mockAuthAP<PERSON>, mockCategoriesAPI, mockItemsAPI, mockQrCodeAPI, useMockAPI } from './mockApi';\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('token');\n    window.location.href = '/admin/login';\n  }\n  return Promise.reject(error);\n});\n\n// Real API implementations\nconst realAuthAPI = {\n  login: credentials => api.post('/auth/login', credentials),\n  logout: () => {\n    localStorage.removeItem('token');\n    return Promise.resolve();\n  },\n  isAuthenticated: () => !!localStorage.getItem('token')\n};\nconst realCategoriesAPI = {\n  getAll: () => api.get('/categories'),\n  create: category => api.post('/categories', category),\n  update: (id, category) => api.put(`/categories/${id}`, category),\n  delete: id => api.delete(`/categories/${id}`)\n};\nconst realItemsAPI = {\n  getAll: () => api.get('/items'),\n  getByTableId: tableId => api.get(`/items?tableId=${tableId}`),\n  create: item => api.post('/items', item),\n  update: (id, item) => api.put(`/items/${id}`, item),\n  delete: id => api.delete(`/items/${id}`),\n  uploadImage: (id, formData) => api.post(`/items/${id}/image`, formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  })\n};\nconst realQrCodeAPI = {\n  getQRCode: tableId => api.get(`/qrcode/${tableId}`)\n};\n\n// Export APIs (mock or real based on environment)\nexport const authAPI = useMockAPI ? mockAuthAPI : realAuthAPI;\nexport const categoriesAPI = useMockAPI ? mockCategoriesAPI : realCategoriesAPI;\nexport const itemsAPI = useMockAPI ? mockItemsAPI : realItemsAPI;\nexport const qrCodeAPI = useMockAPI ? mockQrCodeAPI : realQrCodeAPI;\nexport default api;", "map": {"version": 3, "names": ["axios", "mockAuthAPI", "mockCategoriesAPI", "mockItemsAPI", "mockQrCodeAPI", "useMockAPI", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "realAuthAPI", "login", "credentials", "post", "logout", "resolve", "isAuthenticated", "realCategoriesAPI", "getAll", "get", "category", "update", "id", "put", "delete", "realItemsAPI", "getByTableId", "tableId", "item", "uploadImage", "formData", "realQrCodeAPI", "getQRCode", "authAPI", "categoriesAPI", "itemsAPI", "qrCodeAPI"], "sources": ["D:/Hotel/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\nimport {\n  mockAuthAPI,\n  mockCategoriesAPI,\n  mockItemsAPI,\n  mockQrCodeAPI,\n  useMockAPI\n} from './mockApi';\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('token');\n      window.location.href = '/admin/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Real API implementations\nconst realAuthAPI = {\n  login: (credentials) => api.post('/auth/login', credentials),\n  logout: () => {\n    localStorage.removeItem('token');\n    return Promise.resolve();\n  },\n  isAuthenticated: () => !!localStorage.getItem('token'),\n};\n\nconst realCategoriesAPI = {\n  getAll: () => api.get('/categories'),\n  create: (category) => api.post('/categories', category),\n  update: (id, category) => api.put(`/categories/${id}`, category),\n  delete: (id) => api.delete(`/categories/${id}`),\n};\n\nconst realItemsAPI = {\n  getAll: () => api.get('/items'),\n  getByTableId: (tableId) => api.get(`/items?tableId=${tableId}`),\n  create: (item) => api.post('/items', item),\n  update: (id, item) => api.put(`/items/${id}`, item),\n  delete: (id) => api.delete(`/items/${id}`),\n  uploadImage: (id, formData) =>\n    api.post(`/items/${id}/image`, formData, {\n      headers: { 'Content-Type': 'multipart/form-data' }\n    }),\n};\n\nconst realQrCodeAPI = {\n  getQRCode: (tableId) => api.get(`/qrcode/${tableId}`),\n};\n\n// Export APIs (mock or real based on environment)\nexport const authAPI = useMockAPI ? mockAuthAPI : realAuthAPI;\nexport const categoriesAPI = useMockAPI ? mockCategoriesAPI : realCategoriesAPI;\nexport const itemsAPI = useMockAPI ? mockItemsAPI : realItemsAPI;\nexport const qrCodeAPI = useMockAPI ? mockQrCodeAPI : realQrCodeAPI;\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,WAAW,EACXC,iBAAiB,EACjBC,YAAY,EACZC,aAAa,EACbC,UAAU,QACL,WAAW;;AAElB;AACA,MAAMC,GAAG,GAAGN,KAAK,CAACO,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;EACrEC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAN,GAAG,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACO,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;EACvC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,MAAMU,WAAW,GAAG;EAClBC,KAAK,EAAGC,WAAW,IAAK3B,GAAG,CAAC4B,IAAI,CAAC,aAAa,EAAED,WAAW,CAAC;EAC5DE,MAAM,EAAEA,CAAA,KAAM;IACZjB,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOL,OAAO,CAACc,OAAO,CAAC,CAAC;EAC1B,CAAC;EACDC,eAAe,EAAEA,CAAA,KAAM,CAAC,CAACnB,YAAY,CAACC,OAAO,CAAC,OAAO;AACvD,CAAC;AAED,MAAMmB,iBAAiB,GAAG;EACxBC,MAAM,EAAEA,CAAA,KAAMjC,GAAG,CAACkC,GAAG,CAAC,aAAa,CAAC;EACpCjC,MAAM,EAAGkC,QAAQ,IAAKnC,GAAG,CAAC4B,IAAI,CAAC,aAAa,EAAEO,QAAQ,CAAC;EACvDC,MAAM,EAAEA,CAACC,EAAE,EAAEF,QAAQ,KAAKnC,GAAG,CAACsC,GAAG,CAAC,eAAeD,EAAE,EAAE,EAAEF,QAAQ,CAAC;EAChEI,MAAM,EAAGF,EAAE,IAAKrC,GAAG,CAACuC,MAAM,CAAC,eAAeF,EAAE,EAAE;AAChD,CAAC;AAED,MAAMG,YAAY,GAAG;EACnBP,MAAM,EAAEA,CAAA,KAAMjC,GAAG,CAACkC,GAAG,CAAC,QAAQ,CAAC;EAC/BO,YAAY,EAAGC,OAAO,IAAK1C,GAAG,CAACkC,GAAG,CAAC,kBAAkBQ,OAAO,EAAE,CAAC;EAC/DzC,MAAM,EAAG0C,IAAI,IAAK3C,GAAG,CAAC4B,IAAI,CAAC,QAAQ,EAAEe,IAAI,CAAC;EAC1CP,MAAM,EAAEA,CAACC,EAAE,EAAEM,IAAI,KAAK3C,GAAG,CAACsC,GAAG,CAAC,UAAUD,EAAE,EAAE,EAAEM,IAAI,CAAC;EACnDJ,MAAM,EAAGF,EAAE,IAAKrC,GAAG,CAACuC,MAAM,CAAC,UAAUF,EAAE,EAAE,CAAC;EAC1CO,WAAW,EAAEA,CAACP,EAAE,EAAEQ,QAAQ,KACxB7C,GAAG,CAAC4B,IAAI,CAAC,UAAUS,EAAE,QAAQ,EAAEQ,QAAQ,EAAE;IACvCvC,OAAO,EAAE;MAAE,cAAc,EAAE;IAAsB;EACnD,CAAC;AACL,CAAC;AAED,MAAMwC,aAAa,GAAG;EACpBC,SAAS,EAAGL,OAAO,IAAK1C,GAAG,CAACkC,GAAG,CAAC,WAAWQ,OAAO,EAAE;AACtD,CAAC;;AAED;AACA,OAAO,MAAMM,OAAO,GAAGjD,UAAU,GAAGJ,WAAW,GAAG8B,WAAW;AAC7D,OAAO,MAAMwB,aAAa,GAAGlD,UAAU,GAAGH,iBAAiB,GAAGoC,iBAAiB;AAC/E,OAAO,MAAMkB,QAAQ,GAAGnD,UAAU,GAAGF,YAAY,GAAG2C,YAAY;AAChE,OAAO,MAAMW,SAAS,GAAGpD,UAAU,GAAGD,aAAa,GAAGgD,aAAa;AAEnE,eAAe9C,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}